mapindex:
	go test -timeout 0 -test.run=xx -cpuprofile cpusimulate.prof -memprofile mem.prof -bench BenchmarkMapIndex
mapint:
	go test -timeout 0 -test.run=xx -cpuprofile cpusimulate.prof -memprofile mem.prof -bench BenchmarkMapInt
mapwords:
	go test -timeout 0 -test.run=xx -cpuprofile cpusimulate.prof -memprofile mem.prof -bench BenchmarkMapStrings
first1:
	go test -timeout 0 -test.run=xx -cpuprofile cpufirst1.prof -memprofile mem.prof -bench BenchmarkFirst1
simulate:
	go test -timeout 0 -test.run=xx -cpuprofile cpusimulate.prof -memprofile mem.prof -bench BenchmarkSimulate
